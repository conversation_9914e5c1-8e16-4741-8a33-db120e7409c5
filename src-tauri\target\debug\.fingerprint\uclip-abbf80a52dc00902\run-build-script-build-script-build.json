{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 5967554706753373512], [3834743577069889284, "build_script_build", false, 6216914667033473633], [13890802266741835355, "build_script_build", false, 18231603058249863249], [3935545708480822364, "build_script_build", false, 17014327129598119523], [16934220019573174942, "build_script_build", false, 7048976345112759161]], "local": [{"RerunIfChanged": {"output": "debug\\build\\uclip-abbf80a52dc00902\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}